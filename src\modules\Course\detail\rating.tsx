/* eslint-disable react-native/no-inline-styles */
import {
  ScrollView,
  Text,
  View,
  TouchableOpacity,
  TextInput,
} from 'react-native';
import {ColorThemes} from '../../../assets/skin/colors';
import {
  Winicon,
  Rating,
  ProgressSlider,
  AppButton,
  FBottomSheet,
  showBottomSheet,
  hideBottomSheet,
  showSnackbar,
  ComponentStatus,
} from 'wini-mobile-components';
import {TypoSkin} from '../../../assets/skin/typography';
import CommentsList from '../../customer/listview/comments';
import {useTranslation} from 'react-i18next';
import {useRatingData} from '../../../redux/hook/ratingHook';
import {
  useRef,
  useState,
  useEffect,
  forwardRef,
  useImperativeHandle,
} from 'react';
import {useDispatch} from 'react-redux';
import {AppDispatch} from '../../../redux/store/store';
import {RatingActions} from '../../../redux/reducers/ratingReducer';
import {randomGID} from '../../../utils/Utils';
import {getDataToAsyncStorage} from '../../../utils/AsyncStorage';
import {StorageContanst} from '../../../Config/Contanst';
import {useSelectorCustomerState} from '../../../redux/hook/customerHook';
import Animated, {FadeIn, FadeOut} from 'react-native-reanimated';

export default function RatingIndex(pros: any) {
  const {t} = useTranslation();
  const {ratings} = useRatingData(pros.data.Id);
  const bottomSheetRef = useRef<any>(null);
  const writeRatingRef = useRef<any>(null);
  const dispatch = useDispatch<AppDispatch>();
  const user = useSelectorCustomerState().data;
  const bought = pros.checkBuy || false; // Lấy trạng thái đã mua từ props


  // Hàm mở form đánh giá
  const openRatingForm = () => {
    // Initialize rating values before opening form
    setRatingValue(5);
    setComment('');

    console.log('Opening rating form with initial values:', {
      ratingValue: 5,
      comment: '',
    });

    showBottomSheet({
      ref: bottomSheetRef,
      enableDismiss: true,
      title: 'Viết đánh giá',
      suffixAction: (
        <AppButton
          title={t('Gửi')}
          backgroundColor={ColorThemes.light.transparent}
          textColor={ColorThemes.light.Primary_Color_Main}
          borderColor="transparent"
          containerStyle={{padding: 8}}
          textStyle={{
            fontWeight: '600',
            fontSize: 16,
          }}
          onPress={() => {
            console.log('Submit button pressed, current values:', {
              ratingValue,
              comment,
            });
            submitRating();
          }}
        />
      ),
      prefixAction: (
        <TouchableOpacity
          onPress={() => hideBottomSheet(bottomSheetRef)}
          style={{padding: 6, alignItems: 'center'}}>
          <Winicon
            src="outline/layout/xmark"
            size={20}
            color={ColorThemes.light.Neutral_Text_Color_Body}
          />
        </TouchableOpacity>
      ),
      children: (
        <WriteRating
          ref={writeRatingRef}
          setRatingValue={setRatingValue}
          setCmt={setComment}
          initialRating={5}
        />
      ),
    });
  };

  const [ratingValue, setRatingValue] = useState<number>(5);
  const [comment, setComment] = useState<string>('');

  // Hàm gửi đánh giá
  const submitRating = async () => {
    // Get the most up-to-date values directly from the ref
    let finalRatingValue = ratingValue;
    let finalComment = comment;

    // Try to get values from the ref if available
    if (writeRatingRef.current) {
      finalRatingValue =
        writeRatingRef.current.getCurrentRating() || ratingValue;
      finalComment = writeRatingRef.current.getCurrentComment() || comment;
    }

    console.log('Submitting rating with values:', {
      finalRatingValue,
      finalComment,
      stateRatingValue: ratingValue,
      stateComment: comment,
    });

    // Enhanced validation
    if (!user) {
      showSnackbar({
        message: 'Vui lòng đăng nhập để đánh giá',
        status: ComponentStatus.ERROR,
      });
      return;
    }

    if (!finalRatingValue) {
      showSnackbar({
        message: 'Vui lòng chọn số sao đánh giá',
        status: ComponentStatus.ERROR,
      });
      return;
    }

    try {
      // Log the final values before submission
      console.log('Final values for submission:', {
        rating: finalRatingValue,
        comment: finalComment,
        fromRef: !!writeRatingRef.current,
      });

      // Tạo dữ liệu đánh giá
      const ratingData = {
        Id: randomGID(),
        CourseId: pros.data.Id,
        CustomerId: user.Id,
        Value: finalRatingValue,
        Message: finalComment || '', // Ensure comment is never undefined
        DateCreated: new Date().getTime(),
      };

      console.log('Rating data to be submitted:', ratingData);

      // Gửi đánh giá lên server
      await dispatch(RatingActions.addRating(ratingData));
      showSnackbar({
        message: 'Gửi đánh giá thành công',
        status: ComponentStatus.SUCCSESS,
      });

      // Đóng form
      hideBottomSheet(bottomSheetRef);

      // Reset form sau khi đóng form
      setTimeout(() => {
        setComment('');
        setRatingValue(5);
      }, 300);
    } catch (error) {
      console.error('Error submitting rating:', error);
      showSnackbar({
        message: 'Có lỗi xảy ra khi gửi đánh giá',
        status: ComponentStatus.ERROR,
      });
    }
  };

  return (
    <ScrollView
      ref={pros.scrollviewRef}
      {...pros.refreshControlProps}
      style={{
        flex: 1,
        backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
        paddingTop: 16,
      }}>
      <FBottomSheet ref={bottomSheetRef} />
      <View
        style={{
          flexDirection: 'row',
          paddingHorizontal: 16,
          paddingVertical: 24,
          backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
        }}>
        <View style={{flex: 1}}>
          <View style={{flexDirection: 'row', alignItems: 'center', gap: 4}}>
            <Text
              style={{
                ...TypoSkin.heading4,
                fontWeight: '700',
                color: ColorThemes.light.Neutral_Text_Color_Title,
              }}>
              {ratings?.averageRating?.toFixed(1) ?? 0.0}
            </Text>
            <Winicon
              src="fill/user interface/star"
              size={24}
              color={ColorThemes.light.Warning_Color_Main}
            />
          </View>
          <Text
            style={{
              ...TypoSkin.title5,
              fontWeight: '700',
              color: ColorThemes.light.Neutral_Text_Color_Subtitle,
            }}>
            {ratings?.totalCount ?? 0} {t('ratings')}
          </Text>
          {bought && user && (
            <AppButton
              title={t('Đánh giá')}
              backgroundColor={ColorThemes.light.Primary_Color_Main}
              textColor={ColorThemes.light.Neutral_Background_Color_Absolute}
              prefixIcon="outline/user interface/edit"
              prefixIconSize={14}
              textStyle={{
                ...TypoSkin.buttonText3,
                color: ColorThemes.light.Neutral_Background_Color_Absolute,
                fontWeight: '600',
              }}
              borderColor="transparent"
              containerStyle={{
                height: 40,
                borderRadius: 8,
                marginTop: 20,
                shadowColor: '#000',
                shadowOffset: {width: 0, height: 2},
                shadowOpacity: 0.1,
                shadowRadius: 3,
                elevation: 2,
              }}
              onPress={openRatingForm}
            />
          )}
        </View>
        <View style={{flex: 2, gap: 4, marginLeft: 40}}>
          {Array.from({length: 5}, (_, i) => ({rateItem: i}))
            .reverse()
            .map(({rateItem}: {rateItem: number}) => {
              return (
                <View
                  key={rateItem}
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    gap: 4,
                  }}>
                  <View
                    style={{
                      height: 8,
                      width: '45%',
                    }}>
                    <ProgressSlider
                      height={6}
                      progress={
                        (ratings?.listP.filter(
                          (item: any) => item.Value === rateItem + 1,
                        )?.length ?? 0) / (ratings?.totalCount ?? 1)
                      }
                      backgroundColor={
                        ColorThemes.light.Neutral_Background_Color_Main
                      }
                      progressColor={ColorThemes.light.Primary_Color_Main}
                    />
                  </View>
                  <Rating value={rateItem + 1} size={16} style={{gap: 2}} />
                  <Text
                    style={{
                      ...TypoSkin.label4,
                      fontWeight: '700',
                      color: ColorThemes.light.Neutral_Text_Color_Subtitle,
                    }}>
                    (
                    {ratings?.listP.filter(
                      (item: any) => item.Value === rateItem + 1,
                    )?.length ?? 0}
                    )
                  </Text>
                </View>
              );
            })}
        </View>
      </View>
      <CommentsList Id={pros.data.Id} />
      <View style={{height: 100, width: '100%'}} />
    </ScrollView>
  );
}

const WriteRating = forwardRef(
  ({setRatingValue, setCmt, initialRating}: any, ref) => {
    const [rating, setRating] = useState(initialRating);
    const [commentText, setCommentText] = useState('');
    const {t} = useTranslation();

    // Create refs to store the latest values
    const ratingRef = useRef(initialRating);
    const commentRef = useRef('');

    // Expose methods to parent component through ref
    useImperativeHandle(ref, () => ({
      getCurrentRating: () => rating,
      getCurrentComment: () => commentText,
      setRating: (value: number) => {
        setRating(value);
        ratingRef.current = value;
      },
      setComment: (text: string) => {
        setCommentText(text);
        commentRef.current = text;
      },
    }));

    // Update refs when state changes
    useEffect(() => {
      ratingRef.current = rating;
      // Update parent state immediately
      if (setRatingValue) {
        setRatingValue(rating);
      }
    }, [rating, setRatingValue]);

    useEffect(() => {
      commentRef.current = commentText;
      // Update parent state immediately
      if (setCmt) {
        setCmt(commentText);
      }
    }, [commentText, setCmt]);

    // Ensure parent state is updated on component mount
    useEffect(() => {
      // Initialize parent component state with default values
      if (setRatingValue) {
        setRatingValue(initialRating);
      }
      if (setCmt) {
        setCmt('');
      }

      // Expose values to parent through global object for direct access
      const globalRatingData = {
        getCurrentRating: () => ratingRef.current,
        getCurrentComment: () => commentRef.current,
      };

      // @ts-ignore
      window.ratingFormData = globalRatingData;

      return () => {
        // Clean up
        // @ts-ignore
        delete window.ratingFormData;
      };
    }, [initialRating, setRatingValue, setCmt]);

    // Update parent component when local state changes
    const handleRatingChange = (value: number) => {
      setRating(value);
      ratingRef.current = value;
      if (setRatingValue) {
        setRatingValue(value); // Directly call parent's setter
      }
    };

    const handleCommentChange = (text: string) => {
      setCommentText(text);
      commentRef.current = text;
      if (setCmt) {
        setCmt(text); // Directly call parent's setter
      }
    };

    return (
      <View
        style={{
          padding: 20,
          height: 420,
          width: '100%',
          backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
        }}>
        {/* Rating stars */}
        <View
          style={{
            alignItems: 'center',
            marginBottom: 24,
            backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
            borderRadius: 12,
            padding: 16,
          }}>
          <Text
            style={{
              ...TypoSkin.title3,
              marginBottom: 12,
              color: ColorThemes.light.Neutral_Text_Color_Title,
              fontWeight: '600',
            }}>
            {t('Đánh giá của bạn')}
          </Text>
          {/* Custom Rating Stars */}
          <View style={{flexDirection: 'row', gap: 10}}>
            <View
              accessibilityLabel="rating-form-container"
              testID="rating-form-container"
              nativeID={`rating-form-value-${rating}`}>
              <Rating
                value={rating}
                onChange={handleRatingChange}
                size={36}
                style={{gap: 2}}
              />
            </View>
          </View>
          <Text
            style={{
              ...TypoSkin.subtitle3,
              marginTop: 12,
              color: ColorThemes.light.Neutral_Text_Color_Subtitle,
            }}>
            {rating === 1 && t('Rất không hài lòng')}
            {rating === 2 && t('Không hài lòng')}
            {rating === 3 && t('Bình thường')}
            {rating === 4 && t('Hài lòng')}
            {rating === 5 && t('Rất hài lòng')}
          </Text>
        </View>

        {/* Comment input */}
        <View style={{marginBottom: 20}}>
          <Text
            style={{
              ...TypoSkin.title4,
              marginBottom: 12,
              color: ColorThemes.light.Neutral_Text_Color_Title,
              fontWeight: '600',
            }}>
            {t('Nhận xét của bạn')}
          </Text>
          <TextInput
            style={{
              borderWidth: 1,
              borderColor: ColorThemes.light.Neutral_Background_Color_Main,
              borderRadius: 8,
              padding: 16,
              height: 120,
              textAlignVertical: 'top',
              fontSize: 16,
              backgroundColor: '#fff',
              color: ColorThemes.light.Neutral_Text_Color_Body,
            }}
            value={commentText}
            multiline
            placeholder={t('Chia sẻ trải nghiệm học tập của bạn...')}
            onChangeText={handleCommentChange}
            placeholderTextColor={
              ColorThemes.light.Neutral_Text_Color_Placeholder
            }
            autoCapitalize="sentences"
            maxLength={500}
            accessibilityLabel="rating-form-comment"
            testID="rating-form-comment"
            nativeID={`rating-form-comment-${commentText}`}
          />
        </View>
      </View>
    );
  },
);
